[package]
name = "gpugovernor"
version = "2.7.0"
edition = "2021"
authors = ["Rust Port: Seyud"]
description = "Mediatek Mali GPU Load-Based Frequency Adjustment"

[[bin]]
name = "gpugovernor"
path = "src/main.rs"

[dependencies]
log = "0.4"
env_logger = "0.11"
chrono = "0.4"
inotify = "0.11"
nix = "0.30"
libc = "0.2"
anyhow = "1.0"
thiserror = "2.0"
once_cell = "1.17"
regex = "1.11.1"
paste = "1.0"
dumpsys-rs = { git = "https://github.com/shadow3aaa/dumpsys-rs" }

[profile.release]
overflow-checks = false
codegen-units = 1
lto = "fat"
opt-level = 3
strip = true
