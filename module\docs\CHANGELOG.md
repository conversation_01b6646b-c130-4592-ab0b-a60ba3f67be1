# 📝 更新日志 (CHANGELOG)

## 🎯 v2.6 → v2.7 (2025年6月15日)

### ✨ 新增特性

#### 🎨 WebUI 界面全面重构
- **SVG矢量图标系统** 
  - 替换简单emoji为精美SVG矢量图标
  - 太阳图标新增光线动画和装饰圆环
  - 月亮图标新增表面纹理和星星装饰

- **全新标签页式日志等级选择器**
  - Debug/Info/Warn/Error 四个级别各有专属SVG图标
  - 支持实时预览和视觉反馈

- **多标签页日志查看器**
  - 主日志 (gpu_gov.log) 和初始化日志 (initsvc.log) 分离显示
  - 每个标签页配备专属图标和状态指示
  - 支持标签页间快速切换

- **响应式网格布局系统**
  - 适配不同屏幕尺寸的界面布局

- **用户安全提示系统** ⚠️
  - 添加重要警告提示功能
  - 提醒用户调整电压以防止死机和卡顿
  - 增强用户使用安全性

#### 📱 芯片支持扩展
- **新增 Dimensity 720 配置** 🆕
  - 添加 `mtd720.conf` 配置文件，支持12个频率档位
  - 频率范围：219MHz - 1068MHz  
  - 电压范围：45000μV - 60625μV
  - 优化的DDR设置策略
  - **仅为配置预适配，核心支持待后续版本支持**

### 🔧 功能改进

#### 🎯 GPU调频机制优化
- **警告限流器优化** ⏱️
  - 限流时间从30秒延长至60秒
  - 显著降低系统误报率
  - 提升用户体验稳定性

- **日志显示系统改进** 📋
  - 优化日志输出格式和可读性
  - 增强调试信息的有效性
  - 提升开发者诊断效率

- **v2驱动频率写入机制优化** ⚡
  - 改进设备频率写入的可靠性
  - 提升频率调整的响应速度
  - 增强系统兼容性

- **调试体系完善** 🔍
  - 主程序新增 `debug` 级别日志支持
  - 增强开发者诊断工具的可用性

- **文档维护优化** 📚
  - 移除README中已废弃的Discord频道链接
  - 清理过时的社区联系方式
  - 保持文档信息的准确性

### 🐛 问题修复

#### 🔄 驱动兼容性修复
- **v1驱动空闲模式处理修复** 🛠️
  - 修复设备空闲模式设置逻辑
  - 正确恢复动态调频功能
  - 确保低负载场景下的功率管理

- **天玑9000频率显示修复** 🔧
  - 添加DCS Policy状态检查与禁用功能
  - 修复天玑9000出现两位数频率导致卡顿的问题
  - 提升高端芯片的调频稳定性



